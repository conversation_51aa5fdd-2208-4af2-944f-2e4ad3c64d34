import React, { useEffect, useRef } from 'react';
import { Conversation, Message } from '../types';
import MessageBubble from './MessageBubble';
import MessageInput from './MessageInput';

interface MessageThreadProps {
  conversation: Conversation;
  messages: Message[];
  currentUserId: string;
}

const MessageThread: React.FC<MessageThreadProps> = ({
  conversation,
  messages,
  currentUserId
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const getOtherParticipant = () => {
    return conversation.participantDetails.find(p => p.id !== currentUserId);
  };

  const otherParticipant = getOtherParticipant();

  if (!otherParticipant) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-neutral-muted">Error loading conversation</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-neutral-border bg-neutral-surface">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <img
              src={otherParticipant.avatarUrl}
              alt={otherParticipant.username}
              className="w-10 h-10 rounded-full border-2 border-brand-primary/50"
            />
            {otherParticipant.isActive && (
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-neutral-surface animate-pulse"></div>
            )}
          </div>
          <div>
            <h3 className="font-semibold text-neutral-100 animate-text-glow">
              {otherParticipant.username}
            </h3>
            <p className="text-sm text-neutral-muted">
              {otherParticipant.isActive ? 'Active' : 'Offline'}
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-neutral-base">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-4xl mb-2">👋</div>
              <p className="text-neutral-muted">
                Start the conversation with {otherParticipant.username}
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isCurrentUser = message.senderId === currentUserId;
              const showAvatar = !isCurrentUser && (
                index === 0 || 
                messages[index - 1].senderId !== message.senderId
              );

              return (
                <MessageBubble
                  key={message.id}
                  message={message}
                  isCurrentUser={isCurrentUser}
                  showAvatar={showAvatar}
                />
              );
            })}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="border-t border-neutral-border bg-neutral-surface">
        <MessageInput
          conversationId={conversation.id}
          currentUserId={currentUserId}
          placeholder={`Message ${otherParticipant.username}...`}
        />
      </div>
    </div>
  );
};

export default MessageThread;
