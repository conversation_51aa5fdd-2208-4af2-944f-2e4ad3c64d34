import React from 'react';
import { Message, MessageStatus } from '../types';
import { format } from 'date-fns';

interface MessageBubbleProps {
  message: Message;
  isCurrentUser: boolean;
  showAvatar: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isCurrentUser,
  showAvatar
}) => {
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
      
      if (diffInHours < 24) {
        return format(date, 'HH:mm');
      } else if (diffInHours < 168) { // 7 days
        return format(date, 'EEE HH:mm');
      } else {
        return format(date, 'MMM dd, HH:mm');
      }
    } catch {
      return 'Unknown';
    }
  };

  const getStatusIcon = (status: MessageStatus) => {
    switch (status) {
      case MessageStatus.SENDING:
        return <div className="w-3 h-3 border border-neutral-muted border-t-transparent rounded-full animate-spin"></div>;
      case MessageStatus.SENT:
        return <span className="text-neutral-muted">✓</span>;
      case MessageStatus.DELIVERED:
        return <span className="text-neutral-muted">✓✓</span>;
      case MessageStatus.READ:
        return <span className="text-brand-primary">✓✓</span>;
      case MessageStatus.FAILED:
        return <span className="text-red-500">⚠</span>;
      default:
        return null;
    }
  };

  return (
    <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} items-end space-x-2`}>
      {/* Avatar for other user */}
      {!isCurrentUser && (
        <div className="w-8 h-8 flex-shrink-0">
          {showAvatar && (
            <img
              src={message.senderAvatarUrl}
              alt={message.senderUsername}
              className="w-8 h-8 rounded-full border border-brand-primary/30"
            />
          )}
        </div>
      )}

      {/* Message Content */}
      <div className={`max-w-xs lg:max-w-md ${isCurrentUser ? 'order-1' : 'order-2'}`}>
        {/* Username for other user */}
        {!isCurrentUser && showAvatar && (
          <p className="text-xs text-neutral-muted mb-1 ml-2">
            {message.senderUsername}
          </p>
        )}

        {/* Message Bubble */}
        <div
          className={`px-4 py-2 rounded-2xl relative transition-all duration-200 hover-scale ${
            isCurrentUser
              ? 'bg-brand-primary text-white rounded-br-md animate-pulse-glow'
              : 'bg-neutral-surface text-neutral-100 rounded-bl-md border border-neutral-border hover-glow'
          }`}
        >
          <p className="text-sm whitespace-pre-wrap break-words">
            {message.content}
          </p>

          {/* Timestamp and Status */}
          <div className={`flex items-center justify-end mt-1 space-x-1 ${
            isCurrentUser ? 'text-white/70' : 'text-neutral-muted'
          }`}>
            <span className="text-xs">
              {formatTimestamp(message.timestamp)}
            </span>
            {isCurrentUser && (
              <div className="flex items-center">
                {getStatusIcon(message.status)}
              </div>
            )}
          </div>

          {/* Message tail */}
          <div
            className={`absolute bottom-0 w-3 h-3 ${
              isCurrentUser
                ? 'right-0 transform translate-x-1 bg-brand-primary'
                : 'left-0 transform -translate-x-1 bg-neutral-surface border-l border-b border-neutral-border'
            }`}
            style={{
              clipPath: isCurrentUser
                ? 'polygon(0 0, 100% 0, 0 100%)'
                : 'polygon(100% 0, 100% 100%, 0 0)'
            }}
          />
        </div>
      </div>

      {/* Spacer for current user to maintain alignment */}
      {isCurrentUser && <div className="w-8 h-8 flex-shrink-0" />}
    </div>
  );
};

export default MessageBubble;
