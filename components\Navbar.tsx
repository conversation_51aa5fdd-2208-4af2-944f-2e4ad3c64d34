
import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { APP_NAME } from '../constants';
import HomeIcon from './icons/HomeIcon';
import PlusCircleIcon from './icons/PlusCircleIcon';
import UserCircleIcon from './icons/UserCircleIcon';
import AdminIcon from './icons/AdminIcon';
import MessagesIcon from './icons/MessagesIcon';
import { useAuth } from '../hooks/useAuth';

import ContactAdmin from './ContactAdmin';

const Navbar: React.FC = () => {
  const { currentUser, logout, isAdmin } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [showContactAdmin, setShowContactAdmin] = useState(false);

  const navItems = [
    { path: '/', icon: HomeIcon, label: 'Home' },
    { path: '/messages', icon: MessagesIcon, label: 'Messages', requiresLogin: true },
    { path: '/create', icon: PlusCircleIcon, label: 'Create Post', requiresLogin: true },
    { path: '/profile', icon: UserCircleIcon, label: 'Profile', requiresLogin: true },
  ];

  const handleLogout = () => {
    logout();
    navigate('/login'); // Redirect to login page after logout
  };


  return (
    <nav className="bg-neutral-surface border-b border-neutral-border shadow-lg sticky top-0 z-50 cyber-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="text-2xl font-logo font-bold text-brand-primary hover:text-brand-secondary transition-all duration-300 hover-text-glow animate-text-glow">
            {APP_NAME}
          </Link>

          {currentUser && ( // Only show main nav icons if logged in
            <div className="flex items-center space-x-2 sm:space-x-3">
              {navItems.map(item => (
                  <Link
                    key={item.path}
                    to={item.path}
                    title={item.label}
                    className={`p-2 rounded-md transition-all duration-200 hover-scale hover-glow ${
                      location.pathname === item.path
                        ? 'text-brand-primary bg-neutral-base animate-pulse-glow'
                        : 'text-neutral-muted hover:text-neutral-100 hover:bg-neutral-base/50'
                    }`}
                  >
                    <item.icon className="w-6 h-6 sm:w-7 sm:h-7" />
                  </Link>
                ))}
              {isAdmin && (
                <Link
                  to="/admin"
                  title="Admin Panel"
                  className={`p-2 rounded-md transition-all duration-200 hover-scale hover-glow animate-cyber-flicker ${
                    location.pathname === '/admin' || location.pathname.startsWith('/admin/')
                      ? 'text-brand-primary bg-neutral-base animate-pulse-glow'
                      : 'text-neutral-muted hover:text-neutral-100 hover:bg-neutral-base/50'
                  }`}
                >
                  <AdminIcon className="w-6 h-6 sm:w-7 sm:h-7" />
                </Link>
              )}
            </div>
          )}

          <div className="flex items-center space-x-3">
            {currentUser ? (
              <>
                <Link to={`/user/${currentUser.id}`} title="View your profile">
                  <img src={currentUser.avatarUrl} alt={currentUser.username} className="w-8 h-8 rounded-full border-2 border-brand-primary hover-scale transition-transform duration-200 animate-pulse-glow cursor-pointer"/>
                </Link>
                <button
                  onClick={() => setShowContactAdmin(true)}
                  className="text-neutral-muted hover:text-brand-primary text-sm font-medium transition-all duration-200 hover-scale hover-text-glow"
                  title="Contact Admin"
                >
                  Contact
                </button>
                <button
                  onClick={handleLogout}
                  className="text-neutral-muted hover:text-brand-primary text-sm font-medium transition-all duration-200 hover-scale hover-text-glow"
                >
                  Logout
                </button>
              </>
            ) : (
              // Logged out state: LoginPage handles login options. Navbar can show Sign Up.
              <Link to="/signup" className="text-neutral-muted hover:text-brand-primary text-sm font-medium transition-colors">
                Sign Up
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* Contact Admin Modal */}
      {showContactAdmin && (
        <ContactAdmin onClose={() => setShowContactAdmin(false)} />
      )}
    </nav>
  );
};

export default Navbar;
